import type {Executor} from '../';
import type {CreateEquipmentSupplierContactRequest, UpdateEquipmentSupplierContactRequest} from '../model/static/';

/**
 * 创建供应商联系人表控制器
 * 
 */
export class EquipmentSupplierContactCmdController {
    
    constructor(private executor: Executor) {}
    
    readonly batchDeleteEquipmentSupplier: (options: EquipmentSupplierContactCmdControllerOptions['batchDeleteEquipmentSupplier']) => Promise<
        void
    > = async(options) => {
        let _uri = '/equipment/supplier/contact/batch/';
        _uri += encodeURIComponent(options.ids);
        return (await this.executor({uri: _uri, method: 'DELETE'})) as Promise<void>;
    }
    
    readonly createEquipmentSupplierContact: (options: EquipmentSupplierContactCmdControllerOptions['createEquipmentSupplierContact']) => Promise<
        void
    > = async(options) => {
        let _uri = '/equipment/supplier/contact';
        return (await this.executor({uri: _uri, method: 'POST', body: options.body})) as Promise<void>;
    }
    
    readonly deleteEquipmentSupplier: (options: EquipmentSupplierContactCmdControllerOptions['deleteEquipmentSupplier']) => Promise<
        void
    > = async(options) => {
        let _uri = '/equipment/supplier/contact/';
        _uri += encodeURIComponent(options.equipmentSupplierContactId);
        return (await this.executor({uri: _uri, method: 'DELETE'})) as Promise<void>;
    }
    
    readonly updateEquipmentSupplier: (options: EquipmentSupplierContactCmdControllerOptions['updateEquipmentSupplier']) => Promise<
        void
    > = async(options) => {
        let _uri = '/equipment/supplier/contact';
        return (await this.executor({uri: _uri, method: 'PUT', body: options.body})) as Promise<void>;
    }
}

export type EquipmentSupplierContactCmdControllerOptions = {
    'createEquipmentSupplierContact': {
        readonly body: CreateEquipmentSupplierContactRequest
    }, 
    'updateEquipmentSupplier': {
        readonly body: UpdateEquipmentSupplierContactRequest
    }, 
    'deleteEquipmentSupplier': {
        readonly equipmentSupplierContactId: number
    }, 
    'batchDeleteEquipmentSupplier': {
        readonly ids: ReadonlyArray<number>
    }
}
