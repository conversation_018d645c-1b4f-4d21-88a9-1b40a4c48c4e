import type {Executor} from '../';
import type {CreateEquipmentManufacturerContactRequest, UpdateEquipmentManufacturerContactRequest} from '../model/static/';

/**
 * 制造商联系人
 * 
 */
export class EquipmentManufacturerContactCmdController {
    
    constructor(private executor: Executor) {}
    
    readonly batchDeleteEquipmentManufacturer: (options: EquipmentManufacturerContactCmdControllerOptions['batchDeleteEquipmentManufacturer']) => Promise<
        void
    > = async(options) => {
        let _uri = '/equipment/manufacturer/contact/batch/';
        _uri += encodeURIComponent(options.ids);
        return (await this.executor({uri: _uri, method: 'DELETE'})) as Promise<void>;
    }
    
    readonly createEquipmentManufacturerContact: (options: EquipmentManufacturerContactCmdControllerOptions['createEquipmentManufacturerContact']) => Promise<
        void
    > = async(options) => {
        let _uri = '/equipment/manufacturer/contact';
        return (await this.executor({uri: _uri, method: 'POST', body: options.body})) as Promise<void>;
    }
    
    readonly deleteEquipmentManufacturer: (options: EquipmentManufacturerContactCmdControllerOptions['deleteEquipmentManufacturer']) => Promise<
        void
    > = async(options) => {
        let _uri = '/equipment/manufacturer/contact/';
        _uri += encodeURIComponent(options.equipmentManufacturerContactId);
        return (await this.executor({uri: _uri, method: 'DELETE'})) as Promise<void>;
    }
    
    readonly updateEquipmentManufacturer: (options: EquipmentManufacturerContactCmdControllerOptions['updateEquipmentManufacturer']) => Promise<
        void
    > = async(options) => {
        let _uri = '/equipment/manufacturer/contact';
        return (await this.executor({uri: _uri, method: 'PUT', body: options.body})) as Promise<void>;
    }
}

export type EquipmentManufacturerContactCmdControllerOptions = {
    'createEquipmentManufacturerContact': {
        readonly body: CreateEquipmentManufacturerContactRequest
    }, 
    'updateEquipmentManufacturer': {
        readonly body: UpdateEquipmentManufacturerContactRequest
    }, 
    'deleteEquipmentManufacturer': {
        readonly equipmentManufacturerContactId: number
    }, 
    'batchDeleteEquipmentManufacturer': {
        readonly ids: ReadonlyArray<number>
    }
}
