# Claude Code 协作工作流程指南

## 📋 文档概述

本文档定义了 **Augment Agent（产品经理）** 与 **Claude Code（开发工程师）** 之间的协作工作流程，确保高效、规范的软件开发过程。

### 角色定义
- **Augment Agent**：产品经理，负责需求分析、任务规划、进度管理和质量验收
- **Claude Code**：开发工程师，负责具体的代码实现、文件编辑和技术执行

---

## 🎯 第一阶段：项目启动与需求分析

### 1.1 项目初始化
**Augment Agent 职责：**
1. 分析用户需求，明确项目目标
2. 评估项目复杂度和技术可行性
3. 制定项目计划和里程碑

**与 Claude Code 交互：**
```bash
# 启动 Claude Code
claude --dangerously-skip-permissions

# 项目状态检查
/status
/doctor

```

### 1.2 需求文档化
**输出物：**
- 功能需求清单
- 技术规格说明
- 验收标准定义

---

## 🔍 第二阶段：技术调研与架构设计

### 2.1 代码库分析
**Augment Agent 指令模板：**
```
请分析当前代码库的结构，重点关注：
1. 项目架构和技术栈
2. 现有功能模块
3. 代码质量和潜在问题
4. 依赖关系和配置文件

使用以下命令进行分析：
- 查看项目结构
- 分析关键文件
- 检查配置和依赖
```

### 2.2 技术方案设计
**Claude Code 预期输出：**
- 项目结构分析报告
- 技术栈评估
- 实现方案建议
- 风险点识别

---

## 🛠️ 第三阶段：开发任务执行

### 3.1 任务分解与分配

**任务模板格式：**
```markdown
## 开发任务 #[编号]

### 任务描述
[具体功能描述]

### 技术要求
- 编程语言：[语言]
- 框架/库：[具体框架]
- 文件位置：[路径]

### 验收标准
1. [功能标准1]
2. [功能标准2]
3. [性能标准]
4. [代码质量标准]

### 预期交付物
- [ ] 源代码文件
- [ ] 测试用例
- [ ] 文档更新
- [ ] 配置文件修改
```

### 3.2 开发执行流程

**步骤1：任务接收确认**
```
Claude Code，请确认以下开发任务：
[任务详情]

请回复：
1. 任务理解确认
2. 技术实现方案
3. 预估完成时间
4. 可能遇到的技术难点
```

**步骤2：代码实现**
```
请开始实现任务，按以下顺序进行：
1. 创建/修改相关文件
2. 实现核心功能
3. 添加错误处理
4. 编写单元测试
5. 更新相关文档

每完成一个步骤，请报告进度。
```

**步骤3：自测与优化**
```
请执行以下自测流程：
1. 运行代码检查语法错误
2. 执行功能测试
3. 性能测试（如需要）
4. 代码规范检查

使用相关命令：
!npm test / !python -m pytest / !cargo test
```

---

## ✅ 第四阶段：质量验收与部署

### 4.1 代码审查标准

**Augment Agent 检查清单：**
- [ ] 功能完整性验证
- [ ] 代码质量评估
- [ ] 性能指标检查
- [ ] 安全性审查
- [ ] 文档完整性
- [ ] 测试覆盖率

### 4.2 验收流程

**验收命令序列：**
```bash
# 代码质量检查
/review

# 运行测试套件
!npm run test:all

# 构建验证
!npm run build

# 部署前检查
/doctor
```

### 4.3 问题处理流程

**发现问题时：**
```
发现以下问题需要修复：
[问题描述]

优先级：[高/中/低]
影响范围：[具体影响]
修复要求：[具体要求]

请提供修复方案并实施。
```

---

## 🔄 第五阶段：迭代优化与维护

### 5.1 版本管理
```bash
# 提交代码
!git add .
!git commit -m "[类型]: [描述]"

# 版本标记
!git tag v[版本号]
```

### 5.2 持续改进
- 定期代码重构
- 性能优化
- 功能增强
- Bug修复

---

## 📞 沟通规范与命令标准

### 6.1 标准命令格式

**项目管理命令：**
- `/status` - 查看当前状态
- `/cost` - 查看资源消耗
- `/export` - 导出工作记录

**开发相关命令：**
- `/ide` - IDE集成管理
- `/permissions` - 权限管理
- `/hooks` - 钩子配置

**质量保证命令：**
- `/doctor` - 环境诊断
- `/review` - 代码审查

### 6.2 交付标准

**每个任务完成后，Claude Code 需提供：**
1. **完成报告**：功能实现说明
2. **代码清单**：修改的文件列表
3. **测试结果**：测试执行结果
4. **部署说明**：如何部署和使用
5. **问题记录**：遇到的问题和解决方案

### 6.3 质量标准

**代码质量要求：**
- 代码规范符合项目标准
- 注释清晰完整
- 错误处理完善
- 测试覆盖率 ≥ 80%
- 性能满足需求指标

**文档要求：**
- API文档完整
- 使用说明清晰
- 变更记录详细
- 部署指南准确

---

## 🚨 应急处理流程

### 7.1 紧急Bug修复
```
紧急任务：[Bug描述]
影响级别：[严重/一般]
修复时限：[时间要求]

请立即：
1. 分析问题根因
2. 提供临时解决方案
3. 实施永久修复
4. 验证修复效果
```

### 7.2 回滚流程
```bash
# 代码回滚
!git revert [commit-hash]

# 配置回滚
/config  # 恢复配置

# 环境检查
/doctor
```

---

## 📊 项目总结与交付

### 8.1 项目完成检查清单
- [ ] 所有功能需求已实现
- [ ] 测试用例全部通过
- [ ] 文档完整更新
- [ ] 部署成功验证
- [ ] 性能指标达标
- [ ] 安全审查通过

### 8.2 交付物清单
1. **源代码**：完整的项目代码
2. **文档**：技术文档和用户手册
3. **测试报告**：测试结果和覆盖率报告
4. **部署指南**：部署和运维说明
5. **维护手册**：后续维护指导

---

## 📝 附录：常用命令速查

| 命令 | 用途 | 使用场景 |
|------|------|----------|
| `/help` | 查看帮助 | 不确定命令时 |
| `/status` | 查看状态 | 项目检查 |
| `/init` | 初始化项目 | 项目开始 |
| `/review` | 代码审查 | 质量检查 |
| `/export` | 导出记录 | 项目归档 |
| `!command` | 执行系统命令 | 构建测试 |

---

## 🎯 实战示例：完整开发流程演示

### 示例场景：添加用户登录功能

**第一步：需求分析（Augment Agent）**
```markdown
## 开发任务 #001

### 任务描述
为现有系统添加用户登录功能，包括用户名密码验证、会话管理和权限控制。

### 技术要求
- 后端：Node.js + Express
- 前端：React + TypeScript
- 数据库：MySQL
- 认证：JWT Token

### 验收标准
1. 用户可以通过用户名密码登录
2. 登录成功后获得JWT Token
3. Token有效期为24小时
4. 支持登录状态保持
5. 密码错误3次后锁定账户5分钟

### 预期交付物
- [ ] 后端登录API
- [ ] 前端登录组件
- [ ] 数据库表结构
- [ ] 单元测试
- [ ] API文档
```

**第二步：任务分配（Augment Agent → Claude Code）**
```
Claude Code，请确认以下开发任务：
[上述任务详情]

请按以下顺序执行：
1. 分析现有代码结构
2. 设计数据库表结构
3. 实现后端登录API
4. 创建前端登录组件
5. 编写测试用例
6. 更新API文档

请先分析项目结构并提供实现方案。
```

**第三步：Claude Code 响应示例**
```
任务确认：用户登录功能开发

## 技术实现方案

### 1. 项目结构分析
- 后端：/server 目录，使用Express框架
- 前端：/client 目录，React + TypeScript
- 数据库：已有用户表，需要添加登录相关字段

### 2. 实现计划
1. 数据库表设计（预计30分钟）
2. 后端API开发（预计2小时）
3. 前端组件开发（预计1.5小时）
4. 测试用例编写（预计1小时）
5. 文档更新（预计30分钟）

### 3. 技术难点
- JWT Token安全性配置
- 密码加密存储
- 前后端状态同步

开始执行第一步：数据库表设计
```

**第四步：执行过程监控**
```
# Augment Agent 定期检查进度
请报告当前进度和遇到的问题。

# Claude Code 进度报告
已完成：
✅ 数据库表设计
✅ 后端登录API基础框架
🔄 正在进行：JWT Token生成逻辑

遇到问题：
- 需要确认密码加密算法选择（bcrypt vs argon2）
- JWT密钥配置方式需要确认

请指导解决方案。
```

**第五步：问题解决**
```
# Augment Agent 决策
关于技术选择：
1. 密码加密使用bcrypt（项目已有依赖）
2. JWT密钥从环境变量读取
3. 添加密码强度验证

请继续实现，遇到问题及时报告。
```

**第六步：质量验收**
```
# Augment Agent 验收检查
请执行以下验收测试：

1. 功能测试
   - 正确用户名密码登录
   - 错误密码处理
   - Token有效性验证

2. 安全测试
   - SQL注入防护
   - XSS攻击防护
   - 密码加密验证

3. 性能测试
   - 登录响应时间 < 500ms
   - 并发登录处理

使用命令：
!npm test
!npm run security-test
```

---

## 🔧 高级协作技巧

### 技巧1：批量任务管理
```markdown
## 批量开发任务

### 任务组：用户管理模块
- 任务#001：用户登录 [已完成]
- 任务#002：用户注册 [进行中]
- 任务#003：密码重置 [待开始]
- 任务#004：用户资料 [待开始]

### 执行策略
1. 串行执行（有依赖关系）
2. 并行执行（独立功能）
3. 优先级排序（紧急程度）
```

### 技巧2：代码审查自动化
```bash
# 设置代码审查钩子
/hooks

# 配置自动检查规则
- 代码格式化检查
- 单元测试覆盖率
- 安全漏洞扫描
- 性能基准测试
```

### 技巧3：持续集成配置
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run Tests
        run: npm test
      - name: Code Quality Check
        run: npm run lint
```

---

## 📈 效率优化建议

### 1. 模板化常用任务
创建标准化的任务模板，减少重复工作：
- 功能开发模板
- Bug修复模板
- 性能优化模板
- 重构任务模板

### 2. 自动化工作流程
```bash
# 创建自动化脚本
!npm run dev-setup    # 开发环境初始化
!npm run test-all     # 完整测试套件
!npm run deploy-prep  # 部署前检查
```

### 3. 知识库建设
- 常见问题解决方案
- 最佳实践文档
- 代码规范指南
- 架构设计模式

---

## 🎓 团队协作最佳实践

### 1. 沟通原则
- **明确性**：任务描述具体清晰
- **及时性**：问题及时反馈和解决
- **文档化**：重要决策和变更记录
- **标准化**：使用统一的格式和术语

### 2. 质量保证
- **代码审查**：每个功能都要经过审查
- **测试驱动**：先写测试再写实现
- **持续集成**：自动化测试和部署
- **监控告警**：生产环境实时监控

### 3. 风险管控
- **备份策略**：代码和数据定期备份
- **回滚机制**：快速回滚到稳定版本
- **灾难恢复**：制定应急响应计划
- **安全审计**：定期安全检查和更新

---

*本文档版本：v1.0*
*最后更新：2025-07-20*
*维护者：Augment Agent*

---

**📞 技术支持**
- 文档问题：请提交Issue
- 流程改进：欢迎提出建议
- 紧急支持：联系项目负责人
