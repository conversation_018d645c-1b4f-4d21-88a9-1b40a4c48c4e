import type {Executor} from '../';
import type {EquipmentManufacturerRMDto} from '../model/dto/';
import type {EquipmentManufacturerSpec, Page} from '../model/static/';

export class EquipmentManufacturerQueryController {
    
    constructor(private executor: Executor) {}
    
    readonly getEquipmentManufacturerOptions: () => Promise<
        ReadonlyArray<EquipmentManufacturerRMDto['EquipmentManufacturerQueryController/EQUIPMENT_MANUFACTURER_OPTIONS']>
    > = async() => {
        let _uri = '/equipment/manufacturer/options';
        return (await this.executor({uri: _uri, method: 'GET'})) as Promise<ReadonlyArray<EquipmentManufacturerRMDto['EquipmentManufacturerQueryController/EQUIPMENT_MANUFACTURER_OPTIONS']>>;
    }
    
    readonly getEquipmentManufacturerPage: (options: EquipmentManufacturerQueryControllerOptions['getEquipmentManufacturerPage']) => Promise<
        Page<EquipmentManufacturerRMDto['EquipmentManufacturerQueryController/EQUIPMENT_MANUFACTURER_LIST']>
    > = async(options) => {
        let _uri = '/equipment/manufacturer/page';
        let _separator = _uri.indexOf('?') === -1 ? '?' : '&';
        let _value: any = undefined;
        _value = options.pageIndex;
        if (_value !== undefined && _value !== null) {
            _uri += _separator
            _uri += 'pageIndex='
            _uri += encodeURIComponent(_value);
            _separator = '&';
        }
        _value = options.pageSize;
        if (_value !== undefined && _value !== null) {
            _uri += _separator
            _uri += 'pageSize='
            _uri += encodeURIComponent(_value);
            _separator = '&';
        }
        _value = options.sortCode;
        if (_value !== undefined && _value !== null) {
            _uri += _separator
            _uri += 'sortCode='
            _uri += encodeURIComponent(_value);
            _separator = '&';
        }
        return (await this.executor({uri: _uri, method: 'GET', body: options.body})) as Promise<Page<EquipmentManufacturerRMDto['EquipmentManufacturerQueryController/EQUIPMENT_MANUFACTURER_LIST']>>;
    }
}

export type EquipmentManufacturerQueryControllerOptions = {
    'getEquipmentManufacturerOptions': {}, 
    'getEquipmentManufacturerPage': {
        readonly body: EquipmentManufacturerSpec, 
        readonly pageIndex?: number | undefined, 
        readonly pageSize?: number | undefined, 
        readonly sortCode?: string | undefined
    }
}
