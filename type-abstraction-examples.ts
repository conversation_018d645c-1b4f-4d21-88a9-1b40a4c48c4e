// ===== 类型抽象的不同层次示例 =====

// 原始 API 类型（来自后端）
interface ApiResponse {
  "UserController/GET_USER_LIST": {
    id: number;
    username: string;
    email: string;
    created_at: string;
    is_active: boolean;
  };
  "UserController/GET_USER_OPTIONS": {
    id: number;
    username: string;
  };
}

// ===== 第一层抽象：简化类型名称 =====
// 目的：让复杂的索引类型更易读
type UserListItem = ApiResponse["UserController/GET_USER_LIST"];
type UserOption = ApiResponse["UserController/GET_USER_OPTIONS"];

// ===== 第二层抽象：业务语义抽象 =====
// 目的：根据业务场景重新命名，更符合业务语义
type Employee = UserListItem;  // 在员工管理模块中
type Customer = UserListItem;  // 在客户管理模块中
type Reviewer = UserListItem;  // 在审核模块中

// ===== 第三层抽象：UI 层抽象 =====
// 目的：为 UI 组件添加特有的属性
interface UserDisplayItem extends UserListItem {
  // UI 特有的状态
  isSelected?: boolean;
  isEditing?: boolean;
  
  // 计算属性
  displayName?: string;  // username + email 的组合显示
  statusText?: string;   // is_active 的文字描述
}

// ===== 第四层抽象：表单数据抽象 =====
// 目的：定义表单专用的数据结构
interface UserFormData {
  // 表单字段（可能与 API 字段不完全一致）
  username: string;
  email: string;
  password?: string;     // 新增时需要，编辑时可选
  confirmPassword?: string;  // 仅表单需要
  isActive: boolean;
}

// ===== 第五层抽象：组合类型抽象 =====
// 目的：创建复合的业务类型
interface UserWithPermissions extends UserListItem {
  permissions: string[];
  roles: string[];
}

// 联合类型抽象
type UserStatus = 'active' | 'inactive' | 'pending' | 'suspended';

// 条件类型抽象
type UserWithOptionalId<T extends boolean> = T extends true 
  ? UserListItem 
  : Omit<UserListItem, 'id'>;

// ===== 使用示例 =====

// 1. 简单的类型别名使用
const userList = ref<UserListItem[]>([]);
const userOptions = ref<UserOption[]>([]);

// 2. 业务语义类型使用
const employees = ref<Employee[]>([]);
const customers = ref<Customer[]>([]);

// 3. UI 层类型使用
const displayUsers = ref<UserDisplayItem[]>([]);

// 4. 表单类型使用
const formData = reactive<UserFormData>({
  username: '',
  email: '',
  isActive: true
});

// 5. 组合类型使用
const adminUsers = ref<UserWithPermissions[]>([]);

// ===== 类型抽象 vs 数据抽象的区别 =====

// 类型抽象：只在编译时存在，不影响运行时
type SimpleUser = Pick<UserListItem, 'id' | 'username'>;

// 数据抽象：运行时的数据转换
function toSimpleUser(user: UserListItem): SimpleUser {
  return {
    id: user.id,
    username: user.username
  };
}

// ===== 高级类型抽象示例 =====

// 工具类型抽象
type PartialUser = Partial<UserListItem>;
type RequiredUser = Required<UserListItem>;
type UserKeys = keyof UserListItem;
type UserValues = UserListItem[keyof UserListItem];

// 映射类型抽象
type UserStringFields = {
  [K in keyof UserListItem]: UserListItem[K] extends string ? K : never;
}[keyof UserListItem];

// 模板字面量类型抽象
type UserEventType = `user_${UserStatus}_changed`;

export type {
  UserListItem,
  UserOption,
  Employee,
  Customer,
  UserDisplayItem,
  UserFormData,
  UserWithPermissions,
  UserStatus,
  UserWithOptionalId
};
