{"name": "pure-admin-thin-max-ts", "version": "6.0.0", "private": true, "type": "module", "scripts": {"dev": "NODE_OPTIONS=--max-old-space-size=4096 vite", "serve": "pnpm dev", "build": "rimraf dist && NODE_OPTIONS=--max-old-space-size=8192 vite build", "build:staging": "rimraf dist && vite build --mode staging", "report": "rimraf dist && vite build", "preview": "vite preview", "preview:build": "pnpm build && vite preview", "typecheck": "tsc --noEmit && vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "svgo": "svgo -f . -r", "clean:cache": "rimraf .eslintcache && rimraf pnpm-lock.yaml && rimraf node_modules && pnpm store prune && pnpm install", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock,build}/**/*.{vue,js,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{html,vue,css,scss}\" --cache-location node_modules/.cache/stylelint/", "lint": "pnpm lint:eslint && pnpm lint:prettier && pnpm lint:stylelint", "prepare": "husky", "preinstall": "npx only-allow pnpm", "download:api": "node download-api.js"}, "keywords": ["vue-pure-admin", "element-plus", "tailwindcss", "pure-admin", "typescript", "pinia", "vue3", "vite", "esm"], "bugs": {"url": "https://github.com/pure-admin/vue-pure-admin/issues"}, "license": "MIT", "author": {"name": "xiaoxian521", "email": "<EMAIL>", "url": "https://github.com/xiaoxian521"}, "dependencies": {"@howdyjs/mouse-menu": "^2.1.7", "@imengyu/vue3-context-menu": "^1.4.8", "@pureadmin/descriptions": "^1.2.1", "@pureadmin/table": "^3.2.1", "@pureadmin/utils": "^2.6.0", "@vueuse/core": "^13.2.0", "@vueuse/motion": "^3.0.3", "@zxcvbn-ts/core": "^3.0.4", "animate.css": "^4.1.1", "axios": "^1.9.0", "cropperjs": "^1.6.2", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.9.10", "js-cookie": "^3.0.5", "localforage": "^1.10.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "pinia": "^3.0.2", "pinyin-pro": "^3.26.0", "qs": "^6.14.0", "responsive-storage": "^2.2.0", "sortablejs": "^1.15.6", "vue": "^3.5.13", "vue-router": "^4.5.1", "vue-tippy": "^6.7.0", "vue-types": "^6.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/types": "^19.8.1", "@eslint/js": "^9.26.0", "@faker-js/faker": "^9.8.0", "@iconify/json": "^2.2.338", "@iconify/vue": "4.2.0", "@stagewise-plugins/vue": "^0.6.1", "@stagewise/toolbar-vue": "^0.6.1", "@tailwindcss/vite": "^4.1.6", "@types/js-cookie": "^3.0.6", "@types/node": "^20.17.47", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qs": "^6.9.18", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.1.2", "adm-zip": "^0.5.16", "boxen": "^8.0.1", "code-inspector-plugin": "^0.20.10", "cssnano": "^7.0.7", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-vue": "^10.1.0", "fs-extra": "^11.2.0", "gradient-string": "^3.0.0", "husky": "^9.1.7", "lint-staged": "^15.5.2", "postcss": "^8.5.3", "postcss-html": "^1.8.0", "postcss-load-config": "^6.0.1", "postcss-scss": "^4.0.9", "prettier": "^3.5.3", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^5.14.0", "sass": "^1.88.0", "stylelint": "^16.19.1", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard-scss": "^14.0.0", "stylelint-prettier": "^5.0.3", "svgo": "^3.3.2", "tailwindcss": "^4.1.6", "temp-dir": "^3.0.0", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1", "unplugin-icons": "^22.1.0", "uuid": "^11.0.6", "vite": "^6.3.5", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-fake-server": "^2.2.0", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-router-warn": "^1.0.0", "vite-svg-loader": "^5.1.0", "vue-eslint-parser": "^10.1.3", "vue-tsc": "^2.2.10"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=22.0.0", "pnpm": ">=9"}, "pnpm": {"allowedDeprecatedVersions": {"are-we-there-yet": "*", "sourcemap-codec": "*", "lodash.isequal": "*", "domexception": "*", "w3c-hr-time": "*", "inflight": "*", "npmlog": "*", "rimraf": "*", "stable": "*", "gauge": "*", "abab": "*", "glob": "*"}, "onlyBuiltDependencies": ["@parcel/watcher", "core-js", "es5-ext", "esbuild", "typeit", "vue-demi"], "ignoredBuiltDependencies": ["@tailwindcss/oxide"]}}